import React from 'react';

interface TodoFormProps {
  inputText: string;
  setInputText: (text: string) => void;
  onAddTodo: () => void;
}

const TodoForm: React.FC<TodoFormProps> = ({ inputText, setInputText, onAddTodo }) => {
  return (
    <div className="flex mb-4">
      <input
        type="text"
        value={inputText}
        onChange={(e) => setInputText(e.target.value)}
        onKeyPress={(e) => e.key === 'Enter' && onAddTodo()}
        className="flex-grow p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="Add a new task..."
      />
      <button
        onClick={onAddTodo}
        className="bg-blue-500 text-white p-2 rounded-r-md hover:bg-blue-600 transition-colors"
      >
        Add
      </button>
    </div>
  );
};

export default TodoForm;
