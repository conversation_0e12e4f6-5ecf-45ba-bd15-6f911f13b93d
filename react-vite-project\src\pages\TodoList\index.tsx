import React, { useState } from 'react';
import TodoForm from './components/TodoForm';
import TodoItem from './components/TodoItem';

interface Todo {
  id: number;
  text: string;
}

const TodoListPage: React.FC = () => {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [inputText, setInputText] = useState('');
  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');

  const handleAddTodo = () => {
    if (inputText.trim() === '') return;
    const newTodo: Todo = {
      id: Date.now(),
      text: inputText,
    };
    setTodos([...todos, newTodo]);
    setInputText('');
  };

  const handleDeleteTodo = (id: number) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  const handleStartEdit = (todo: Todo) => {
    setEditingTodoId(todo.id);
    setEditingText(todo.text);
  };

  const handleCancelEdit = () => {
    setEditingTodoId(null);
    setEditingText('');
  };

  const handleUpdateTodo = () => {
    if (editingText.trim() === '' || editingTodoId === null) return;
    setTodos(
      todos.map(todo =>
        todo.id === editingTodoId ? { ...todo, text: editingText } : todo
      )
    );
    setEditingTodoId(null);
    setEditingText('');
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-center text-gray-800">Todo List</h1>
        <TodoForm 
          inputText={inputText} 
          setInputText={setInputText} 
          onAddTodo={handleAddTodo} 
        />
        <ul>
          {todos.map(todo => (
            <TodoItem
              key={todo.id}
              todo={todo}
              editingTodoId={editingTodoId}
              editingText={editingText}
              setEditingText={setEditingText}
              onDelete={handleDeleteTodo}
              onStartEdit={handleStartEdit}
              onUpdate={handleUpdateTodo}
              onCancelEdit={handleCancelEdit}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};

export default TodoListPage;
