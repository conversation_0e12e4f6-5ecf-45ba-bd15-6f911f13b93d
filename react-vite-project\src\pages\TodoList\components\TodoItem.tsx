import React from 'react';

interface Todo {
  id: number;
  text: string;
}

interface TodoItemProps {
  todo: Todo;
  editingTodoId: number | null;
  editingText: string;
  setEditingText: (text: string) => void;
  onDelete: (id: number) => void;
  onStartEdit: (todo: Todo) => void;
  onUpdate: () => void;
  onCancelEdit: () => void;
}

const TodoItem: React.FC<TodoItemProps> = ({
  todo,
  editingTodoId,
  editingText,
  setEditingText,
  onDelete,
  onStartEdit,
  onUpdate,
  onCancelEdit,
}) => {
  const isEditing = editingTodoId === todo.id;

  return (
    <li className="flex items-center justify-between bg-gray-50 p-3 rounded-md mb-2 shadow-sm">
      {isEditing ? (
        <>
          <input
            type="text"
            value={editingText}
            onChange={(e) => setEditingText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && onUpdate()}
            className="flex-grow p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          <div className="flex items-center ml-2">
            <button
              onClick={onUpdate}
              className="bg-green-500 text-white p-2 rounded-md hover:bg-green-600 transition-colors mr-2"
            >
              Save
            </button>
            <button
              onClick={onCancelEdit}
              className="bg-gray-500 text-white p-2 rounded-md hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
          </div>
        </>
      ) : (
        <>
          <span className="text-gray-700">{todo.text}</span>
          <div className="flex items-center">
            <button
              onClick={() => onStartEdit(todo)}
              className="text-blue-500 hover:text-blue-700 font-semibold mr-4"
            >
              Edit
            </button>
            <button
              onClick={() => onDelete(todo.id)}
              className="text-red-500 hover:text-red-700 font-semibold"
            >
              Delete
            </button>
          </div>
        </>
      )}
    </li>
  );
};

export default TodoItem;
