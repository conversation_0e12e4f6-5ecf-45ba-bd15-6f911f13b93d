import React, { useState } from 'react';

const CounterPage: React.FC = () => {
  const [count, setCount] = useState(0);

  const increment = () => setCount(prevCount => prevCount + 1);
  const decrement = () => setCount(prevCount => prevCount - 1);
  const reset = () => setCount(0);

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full max-w-sm text-center">
        <h1 className="text-2xl font-bold mb-4 text-gray-800">Counter</h1>
        <p className="text-6xl font-bold mb-6 text-gray-900">{count}</p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={decrement}
            className="px-6 py-2 bg-red-500 text-white font-semibold rounded-lg shadow-md hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400"
          >
            - Decrease
          </button>
          <button
            onClick={increment}
            className="px-6 py-2 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600 transition-colors focus:outline-none focus:ring-2 focus:ring-green-400"
          >
            + Increase
          </button>
        </div>
        <button
          onClick={reset}
          className="mt-6 px-8 py-2 bg-gray-500 text-white font-semibold rounded-lg shadow-md hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          Reset
        </button>
      </div>
    </div>
  );
};

export default CounterPage;
