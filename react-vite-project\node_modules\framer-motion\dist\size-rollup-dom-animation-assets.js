const t=(t,e,r)=>r>e?e:r<t?t:r,e={};function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){return"string"==typeof t||Array.isArray(t)}const a=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...a];function n(t){return r(t.animate)||o.some(e=>s(t[e]))}function i(t){return Boolean(n(t)||t.variants)}const c="undefined"!=typeof window,l={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},f={};for(const t in l)f[t]={isEnabled:e=>l[t].some(t=>!!e[t])};const d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],p={value:null,addProjectionMetrics:null};function u(t,r){let s=!1,a=!0;const o={delta:0,timestamp:0,isProcessing:!1},n=()=>s=!0,i=d.reduce((t,e)=>(t[e]=function(t,e){let r=new Set,s=new Set,a=!1,o=!1;const n=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1},c=0;function l(e){n.has(e)&&(f.schedule(e),t()),c++,e(i)}const f={schedule:(t,e=!1,o=!1)=>{const i=o&&a?r:s;return e&&n.add(t),i.has(t)||i.add(t),t},cancel:t=>{s.delete(t),n.delete(t)},process:t=>{i=t,a?o=!0:(a=!0,[r,s]=[s,r],r.forEach(l),e&&p.value&&p.value.frameloop[e].push(c),c=0,r.clear(),a=!1,o&&(o=!1,f.process(t)))}};return f}(n,r?e:void 0),t),{}),{setup:c,read:l,resolveKeyframes:f,preUpdate:u,update:m,preRender:g,render:h,postRender:y}=i,v=()=>{const n=e.useManualTiming?o.timestamp:performance.now();s=!1,e.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),l.process(o),f.process(o),u.process(o),m.process(o),g.process(o),h.process(o),y.process(o),o.isProcessing=!1,s&&r&&(a=!1,t(v))};return{schedule:d.reduce((e,r)=>{const n=i[r];return e[r]=(e,r=!1,i=!1)=>(s||(s=!0,a=!0,o.isProcessing||t(v)),n.schedule(e,r,i)),e},{}),cancel:t=>{for(let e=0;e<d.length;e++)i[d[e]].cancel(t)},state:o,steps:i}}const m=t=>e=>"string"==typeof e&&e.startsWith(t),g=m("--"),h=m("var(--"),y=t=>!!h(t)&&v.test(t.split("/*")[0].trim()),v=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,w={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},x={...w,transform:e=>t(0,1,e)},b={...w,default:1},k=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),P=k("deg"),B=k("%"),R=k("px"),T=k("vh"),X=k("vw"),Y=(()=>({...B,parse:t=>B.parse(t)/100,transform:t=>B.transform(100*t)}))(),O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],S=(()=>new Set(O))(),W={...w,transform:Math.round},$={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R,...{rotate:P,rotateX:P,rotateY:P,rotateZ:P,scale:b,scaleX:b,scaleY:b,scaleZ:b,skew:P,skewX:P,skewY:P,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:x,originX:Y,originY:Y,originZ:R},zIndex:W,fillOpacity:x,strokeOpacity:x,numOctaves:W},L=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Z=t=>Boolean(t&&t.getVelocity),V=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),A="data-"+V("framerAppearId"),E={};function F(t,{layout:e,layoutId:r}){return S.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!E[t]||"opacity"===t)}const I={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},M=O.length;function z(t,e,r){const{style:s,vars:a,transformOrigin:o}=t;let n=!1,i=!1;for(const t in e){const r=e[t];if(S.has(t))n=!0;else if(g(t))a[t]=r;else{const e=L(r,$[t]);t.startsWith("origin")?(i=!0,o[t]=e):s[t]=e}}if(e.transform||(n||r?s.transform=function(t,e,r){let s="",a=!0;for(let o=0;o<M;o++){const n=O[o],i=t[n];if(void 0===i)continue;let c=!0;if(c="number"==typeof i?i===(n.startsWith("scale")?1:0):0===parseFloat(i),!c||r){const t=L(i,$[n]);c||(a=!1,s+=`${I[n]||n}(${t}) `),r&&(e[n]=t)}}return s=s.trim(),r?s=r(e,a?"":s):a&&(s="none"),s}(e,t.transform,r):s.transform&&(s.transform="none")),i){const{originX:t="50%",originY:e="50%",originZ:r=0}=o;s.transformOrigin=`${t} ${e} ${r}`}}const C={offset:"stroke-dashoffset",array:"stroke-dasharray"},H={offset:"strokeDashoffset",array:"strokeDasharray"};function D(t,{attrX:e,attrY:r,attrScale:s,pathLength:a,pathSpacing:o=1,pathOffset:n=0,...i},c,l,f){if(z(t,i,l),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p}=t;d.transform&&(p.transform=d.transform,delete d.transform),(p.transform||d.transformOrigin)&&(p.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),p.transform&&(p.transformBox=f?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==r&&(d.y=r),void 0!==s&&(d.scale=s),void 0!==a&&function(t,e,r=1,s=0,a=!0){t.pathLength=1;const o=a?C:H;t[o.offset]=R.transform(-s);const n=R.transform(e),i=R.transform(r);t[o.array]=`${n} ${i}`}(d,a,o,n,!1)}const j=t=>"string"==typeof t&&"svg"===t.toLowerCase(),U=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function K(t){return"string"==typeof t&&!t.includes("-")&&!!(U.indexOf(t)>-1||/[A-Z]/u.test(t))}function q(t){const e=[{},{}];return t?.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function G(t,e,r,s){if("function"==typeof e){const[a,o]=q(s);e=e(void 0!==r?r:t.custom,a,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[a,o]=q(s);e=e(void 0!==r?r:t.custom,a,o)}return e}function J(t,e,r){const{style:s}=t,a={};for(const o in s)(Z(s[o])||e.style&&Z(e.style[o])||F(o,t)||void 0!==r?.getValue(o)?.liveStyle)&&(a[o]=s[o]);return a}function N(t,e,r){const s=J(t,e,r);for(const r in t)if(Z(t[r])||Z(e[r])){s[-1!==O.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]}return s}export{T as A,$ as B,S as C,o as D,a as E,g as F,V as G,e as M,s as a,c as b,u as c,Z as d,F as e,f,z as g,D as h,n as i,j,K as k,i as l,r as m,N as n,A as o,w as p,x as q,G as r,J as s,t,B as u,y as v,R as w,O as x,P as y,X as z};
