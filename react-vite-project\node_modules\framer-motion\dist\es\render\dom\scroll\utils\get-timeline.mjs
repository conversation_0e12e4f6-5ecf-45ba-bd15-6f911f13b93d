import { supportsScrollTimeline } from 'motion-dom';
import { scrollInfo } from '../track.mjs';

const timelineCache = new Map();
function scrollTimelineFallback(options) {
    const currentTime = { value: 0 };
    const cancel = scrollInfo((info) => {
        currentTime.value = info[options.axis].progress * 100;
    }, options);
    return { currentTime, cancel };
}
function getTimeline({ source, container, ...options }) {
    const { axis } = options;
    if (source)
        container = source;
    const containerCache = timelineCache.get(container) ?? new Map();
    timelineCache.set(container, containerCache);
    const targetKey = options.target ?? "self";
    const targetCache = containerCache.get(targetKey) ?? {};
    const axisKey = axis + (options.offset ?? []).join(",");
    if (!targetCache[axisKey]) {
        targetCache[axisKey] =
            !options.target && supportsScrollTimeline()
                ? new ScrollTimeline({ source: container, axis })
                : scrollTimelineFallback({ container, ...options });
    }
    return targetCache[axisKey];
}

export { getTimeline };
