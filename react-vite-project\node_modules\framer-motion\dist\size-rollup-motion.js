import{jsx as t,jsxs as e}from"react/jsx-runtime";import{createContext as n,useContext as i,useId as s,useEffect as o,useCallback as r,Component as a,useMemo as l,useLayoutEffect as h,useRef as u,useInsertionEffect as c,forwardRef as d,Fragment as p,createElement as m}from"react";function f(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function y(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function g(t,e,n,i){if("function"==typeof e){const[s,o]=y(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=y(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function v(t,e,n){const i=t.getProps();return g(i,e,void 0!==n?n:i.custom,t)}function x(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const w=(t,e,n)=>n>e?e:n<t?t:n;const P={},S=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function b(t){return"object"==typeof t&&null!==t}const A=t=>/^0[^.\s]+$/u.test(t);function E(t){let e;return()=>(void 0===e&&(e=t()),e)}const V=t=>t,M=(t,e)=>n=>e(t(n)),D=(...t)=>t.reduce(M),C=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class k{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const R=t=>1e3*t,L=t=>t/1e3;function j(t,e){return e?t*(1e3/e):0}const B=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function F(t,e,n,i){if(t===e&&n===i)return V;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=B(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:B(s(t),e,i)}const O=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,I=t=>e=>1-t(1-e),U=F(.33,1.53,.69,.99),N=I(U),W=O(N),$=t=>(t*=2)<1?.5*N(t):.5*(2-Math.pow(2,-10*(t-1))),Y=t=>1-Math.sin(Math.acos(t)),X=I(Y),K=O(Y),z=F(.42,0,1,1),H=F(0,0,.58,1),q=F(.42,0,.58,1),G=t=>Array.isArray(t)&&"number"==typeof t[0],Z={linear:V,easeIn:z,easeInOut:q,easeOut:H,circIn:Y,circInOut:K,circOut:X,backIn:N,backInOut:W,backOut:U,anticipate:$},_=t=>{if(G(t)){t.length;const[e,n,i,s]=t;return F(e,n,i,s)}return"string"==typeof t?Z[t]:t},J=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Q={value:null,addProjectionMetrics:null};function tt(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=J.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){r.has(e)&&(u.schedule(e),t()),l++,e(a)}const u={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(h),e&&Q.value&&Q.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,u.process(t)))}};return u}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:u,update:c,preRender:d,render:p,postRender:m}=r,f=()=>{const o=P.useManualTiming?s.timestamp:performance.now();n=!1,P.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),h.process(s),u.process(s),c.process(s),d.process(s),p.process(s),m.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(f))};return{schedule:J.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(f)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<J.length;e++)r[J[e]].cancel(t)},state:s,steps:r}}const{schedule:et,cancel:nt,state:it,steps:st}=tt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:V,!0);let ot;function rt(){ot=void 0}const at={now:()=>(void 0===ot&&at.set(it.isProcessing||P.useManualTiming?it.timestamp:performance.now()),ot),set:t=>{ot=t,queueMicrotask(rt)}},lt=t=>e=>"string"==typeof e&&e.startsWith(t),ht=lt("--"),ut=lt("var(--"),ct=t=>!!ut(t)&&dt.test(t.split("/*")[0].trim()),dt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,pt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},mt={...pt,transform:t=>w(0,1,t)},ft={...pt,default:1},yt=t=>Math.round(1e5*t)/1e5,gt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const vt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,xt=(t,e)=>n=>Boolean("string"==typeof n&&vt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Tt=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(gt);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},wt={...pt,transform:t=>Math.round((t=>w(0,255,t))(t))},Pt={test:xt("rgb","red"),parse:Tt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+wt.transform(t)+", "+wt.transform(e)+", "+wt.transform(n)+", "+yt(mt.transform(i))+")"};const St={test:xt("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Pt.transform},bt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),At=bt("deg"),Et=bt("%"),Vt=bt("px"),Mt=bt("vh"),Dt=bt("vw"),Ct=(()=>({...Et,parse:t=>Et.parse(t)/100,transform:t=>Et.transform(100*t)}))(),kt={test:xt("hsl","hue"),parse:Tt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Et.transform(yt(e))+", "+Et.transform(yt(n))+", "+yt(mt.transform(i))+")"},Rt={test:t=>Pt.test(t)||St.test(t)||kt.test(t),parse:t=>Pt.test(t)?Pt.parse(t):kt.test(t)?kt.parse(t):St.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Pt.transform(t):kt.transform(t),getAnimatableNone:t=>{const e=Rt.parse(t);return e.alpha=0,Rt.transform(e)}},Lt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const jt="number",Bt="color",Ft=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ot(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Ft,t=>(Rt.test(t)?(i.color.push(o),s.push(Bt),n.push(Rt.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(jt),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function It(t){return Ot(t).values}function Ut(t){const{split:e,types:n}=Ot(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===jt?yt(t[o]):e===Bt?Rt.transform(t[o]):t[o]}return s}}const Nt=t=>"number"==typeof t?0:Rt.test(t)?Rt.getAnimatableNone(t):t;const Wt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(gt)?.length||0)+(t.match(Lt)?.length||0)>0},parse:It,createTransformer:Ut,getAnimatableNone:function(t){const e=It(t);return Ut(t)(e.map(Nt))}};function $t(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Yt(t,e){return n=>n>0?e:t}const Xt=(t,e,n)=>t+(e-t)*n,Kt=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},zt=[St,Pt,kt];function Ht(t){const e=(n=t,zt.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===kt&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=$t(a,i,t+1/3),o=$t(a,i,t),r=$t(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const qt=(t,e)=>{const n=Ht(t),i=Ht(e);if(!n||!i)return Yt(t,e);const s={...n};return t=>(s.red=Kt(n.red,i.red,t),s.green=Kt(n.green,i.green,t),s.blue=Kt(n.blue,i.blue,t),s.alpha=Xt(n.alpha,i.alpha,t),Pt.transform(s))},Gt=new Set(["none","hidden"]);function Zt(t,e){return n=>Xt(t,e,n)}function _t(t){return"number"==typeof t?Zt:"string"==typeof t?ct(t)?Yt:Rt.test(t)?qt:te:Array.isArray(t)?Jt:"object"==typeof t?Rt.test(t)?qt:Qt:Yt}function Jt(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>_t(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Qt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=_t(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const te=(t,e)=>{const n=Wt.createTransformer(e),i=Ot(t),s=Ot(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Gt.has(t)&&!s.values.length||Gt.has(e)&&!i.values.length?function(t,e){return Gt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):D(Jt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):Yt(t,e)};function ee(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Xt(t,e,n);return _t(t)(t,e)}const ne=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>et.update(e,t),stop:()=>nt(e),now:()=>it.isProcessing?it.timestamp:at.now()}},ie=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},se=2e4;function oe(t){let e=0;let n=t.next(e);for(;!n.done&&e<se;)e+=50,n=t.next(e);return e>=se?1/0:e}function re(t,e,n){const i=Math.max(e-5,0);return j(n-t(i),e-i)}const ae=100,le=10,he=1,ue=0,ce=800,de=.3,pe=.3,me={granular:.01,default:2},fe={granular:.005,default:.5},ye=.01,ge=10,ve=.05,xe=1,Te=.001;function we({duration:t=ce,bounce:e=de,velocity:n=ue,mass:i=he}){let s,o,r=1-e;r=w(ve,xe,r),t=w(ye,ge,L(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=Se(e,r),l=Math.exp(-s);return Te-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),h=Se(Math.pow(e,2),r);return(-s(e)+Te>0?-1:1)*((o-a)*l)/h}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<Pe;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=R(t),isNaN(a))return{stiffness:ae,damping:le,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Pe=12;function Se(t,e){return t*Math.sqrt(1-e*e)}const be=["duration","bounce"],Ae=["stiffness","damping","mass"];function Ee(t,e){return e.some(e=>void 0!==t[e])}function Ve(t=pe,e=de){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:h,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:ue,stiffness:ae,damping:le,mass:he,isResolvedFromDuration:!1,...t};if(!Ee(t,Ae)&&Ee(t,be))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*w(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:he,stiffness:s,damping:o}}else{const n=we(t);e={...e,...n,mass:he},e.isResolvedFromDuration=!0}return e}({...n,velocity:-L(n.velocity||0)}),m=d||0,f=h/(2*Math.sqrt(l*u)),y=r-o,g=L(Math.sqrt(l/u)),v=Math.abs(y)<5;let x;if(i||(i=v?me.granular:me.default),s||(s=v?fe.granular:fe.default),f<1){const t=Se(g,f);x=e=>{const n=Math.exp(-f*g*e);return r-n*((m+f*g*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-g*t)*(y+(m+g*y)*t);else{const t=g*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*g*e),i=Math.min(t*e,300);return r-n*((m+f*g*y)*Math.sinh(i)+t*y*Math.cosh(i))/t}}const T={calculatedDuration:p&&c||null,next:t=>{const e=x(t);if(p)a.done=t>=c;else{let n=0===t?m:0;f<1&&(n=0===t?R(m):re(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(oe(T),se),e=ie(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function Me({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:h=.5,restSpeed:u}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=h,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=Ve({keyframes:[d.value,p(d.value)],velocity:re(v,t,d.value),damping:s,stiffness:o,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function De(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(e.length,1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||P.mix||ee,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||V:e;o=D(t,o)}i.push(o)}return i}(e,i,s),l=a.length,h=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=C(t[i],t[i+1],n);return a[i](s)};return n?e=>h(w(t[0],t[o-1],e)):h}function Ce(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=C(0,e,i);t.push(Xt(n,1,s))}}(e,t.length-1),e}function ke({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(_):_(i),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Ce(e),t),a=De(r,e,{ease:Array.isArray(s)?s:(l=e,h=s,l.map(()=>h||q).splice(0,l.length-1))});var l,h;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}Ve.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(oe(i),se);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:L(s)}}(t,100,Ve);return t.ease=e.ease,t.duration=R(e.duration),t.type="keyframes",t};const Re=t=>null!==t;function Le(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(Re),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const je={decay:Me,inertia:Me,tween:ke,keyframes:ke,spring:Ve};function Be(t){"string"==typeof t.type&&(t.type=je[t.type])}class Fe{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Oe=t=>t/100;class Ie extends Fe{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==at.now()&&this.tick(at.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Be(t);const{type:e=ke,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||ke;a!==ke&&"number"!=typeof r[0]&&(this.mixKeyframes=D(Oe,ee(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=oe(l));const{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:l=0,keyframes:h,repeat:u,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>i;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let v=this.currentTime,x=n;if(u){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,u+1);Boolean(e%2)&&("reverse"===c?(n=1-n,d&&(n-=d/r)):"mirror"===c&&(x=o)),v=w(0,1,n)*r}const T=g?{done:!1,value:h[0]}:x.next(v);s&&(T.value=s(T.value));let{done:P}=T;g||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&p!==Me&&(T.value=Le(h,this.options,f,this.speed)),m&&m(T.value),S&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return L(this.calculatedDuration)}get time(){return L(this.currentTime)}set time(t){t=R(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(at.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=L(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=ne,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(at.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Ue=t=>180*t/Math.PI,Ne=t=>{const e=Ue(Math.atan2(t[1],t[0]));return $e(e)},We={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ne,rotateZ:Ne,skewX:t=>Ue(Math.atan(t[1])),skewY:t=>Ue(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},$e=t=>((t%=360)<0&&(t+=360),t),Ye=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Xe=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Ke={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ye,scaleY:Xe,scale:t=>(Ye(t)+Xe(t))/2,rotateX:t=>$e(Ue(Math.atan2(t[6],t[5]))),rotateY:t=>$e(Ue(Math.atan2(-t[2],t[0]))),rotateZ:Ne,rotate:Ne,skewX:t=>Ue(Math.atan(t[4])),skewY:t=>Ue(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ze(t){return t.includes("scale")?1:0}function He(t,e){if(!t||"none"===t)return ze(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Ke,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=We,s=e}if(!s)return ze(e);const o=i[e],r=s[1].split(",").map(qe);return"function"==typeof o?o(r):r[o]}function qe(t){return parseFloat(t.trim())}const Ge=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ze=(()=>new Set(Ge))(),_e=t=>t===pt||t===Vt,Je=new Set(["x","y","z"]),Qe=Ge.filter(t=>!Je.has(t));const tn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>He(e,"x"),y:(t,{transform:e})=>He(e,"y")};tn.translateX=tn.x,tn.translateY=tn.y;const en=new Set;let nn=!1,sn=!1,on=!1;function rn(){if(sn){const t=Array.from(en).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Qe.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}sn=!1,nn=!1,en.forEach(t=>t.complete(on)),en.clear()}function an(){en.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(sn=!0)})}class ln{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(en.add(this),nn||(nn=!0,et.read(an),et.resolveKeyframes(rn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),en.delete(this)}cancel(){"scheduled"===this.state&&(en.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const hn=E(()=>void 0!==window.ScrollTimeline),un={};function cn(t,e){const n=E(t);return()=>un[e]??n()}const dn=cn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),pn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,mn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pn([0,.65,.55,1]),circOut:pn([.55,0,1,.45]),backIn:pn([.31,.01,.66,-.59]),backOut:pn([.33,1.53,.69,.99])};function fn(t,e){return t?"function"==typeof t?dn()?ie(t,e):"ease-out":G(t)?pn(t):Array.isArray(t)?t.map(t=>fn(t,e)||mn.easeOut):mn[t]:void 0}function yn(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},h=void 0){const u={[e]:n};l&&(u.offset=l);const c=fn(a,s);Array.isArray(c)&&(u.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};h&&(d.pseudoElement=h);return t.animate(u,d)}function gn(t){return"function"==typeof t&&"applyToOptions"in t}class vn extends Fe{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,t.type;const l=function({type:t,...e}){return gn(t)&&dn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=yn(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=Le(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return L(Number(t))}get time(){return L(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=R(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&hn()?(this.animation.timeline=t,V):e(this)}}const xn={anticipate:$,backInOut:W,circInOut:K};function Tn(t){"string"==typeof t.ease&&t.ease in xn&&(t.ease=xn[t.ease])}class wn extends vn{constructor(t){Tn(t),Be(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Ie({...o,autoplay:!1}),a=R(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const Pn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Wt.test(t)&&"0"!==t||t.startsWith("url(")));function Sn(t){return b(t)&&"offsetHeight"in t}const bn=new Set(["opacity","clipPath","filter","transform"]),An=E(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class En extends Fe{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=at.now();const c={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:h,...u},d=h?.KeyframeResolver||ln;this.keyframeResolver=new d(r,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:h}=n;this.resolvedAt=at.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Pn(s,e),a=Pn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||gn(n))&&i)}(t,s,o,r)||(!P.instantAnimations&&a||h?.(Le(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const u={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},c=!l&&function(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!Sn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return An()&&n&&bn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}(u)?new wn({...u,element:u.motionValue.owner.current}):new Ie(u);c.finished.then(()=>this.notifyFinished()).catch(V),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),on=!0,an(),rn(),on=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Vn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Mn(t,e,n=1){const[i,s]=function(t){const e=Vn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return S(t)?parseFloat(t):t}return ct(s)?Mn(s,e,n+1):s}function Dn(t,e){return t?.[e]??t?.default??t}const Cn=new Set(["width","height","top","left","right","bottom",...Ge]),kn=t=>e=>e.test(t),Rn=[pt,Vt,Et,At,Dt,Mt,{test:t=>"auto"===t,parse:t=>t}],Ln=t=>Rn.find(kn(t));function jn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||A(t))}const Bn=new Set(["brightness","contrast","saturate","opacity"]);function Fn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(gt)||[];if(!i)return t;const s=n.replace(i,"");let o=Bn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const On=/\b([a-z-]*)\(.*?\)/gu,In={...Wt,getAnimatableNone:t=>{const e=t.match(On);return e?e.map(Fn).join(" "):t}},Un={...pt,transform:Math.round},Nn={borderWidth:Vt,borderTopWidth:Vt,borderRightWidth:Vt,borderBottomWidth:Vt,borderLeftWidth:Vt,borderRadius:Vt,radius:Vt,borderTopLeftRadius:Vt,borderTopRightRadius:Vt,borderBottomRightRadius:Vt,borderBottomLeftRadius:Vt,width:Vt,maxWidth:Vt,height:Vt,maxHeight:Vt,top:Vt,right:Vt,bottom:Vt,left:Vt,padding:Vt,paddingTop:Vt,paddingRight:Vt,paddingBottom:Vt,paddingLeft:Vt,margin:Vt,marginTop:Vt,marginRight:Vt,marginBottom:Vt,marginLeft:Vt,backgroundPositionX:Vt,backgroundPositionY:Vt,...{rotate:At,rotateX:At,rotateY:At,rotateZ:At,scale:ft,scaleX:ft,scaleY:ft,scaleZ:ft,skew:At,skewX:At,skewY:At,distance:Vt,translateX:Vt,translateY:Vt,translateZ:Vt,x:Vt,y:Vt,z:Vt,perspective:Vt,transformPerspective:Vt,opacity:mt,originX:Ct,originY:Ct,originZ:Vt},zIndex:Un,fillOpacity:mt,strokeOpacity:mt,numOctaves:Un},Wn={...Nn,color:Rt,backgroundColor:Rt,outlineColor:Rt,fill:Rt,stroke:Rt,borderColor:Rt,borderTopColor:Rt,borderRightColor:Rt,borderBottomColor:Rt,borderLeftColor:Rt,filter:In,WebkitFilter:In},$n=t=>Wn[t];function Yn(t,e){let n=$n(t);return n!==In&&(n=Wt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Xn=new Set(["auto","none","0"]);class Kn extends ln{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),ct(i))){const s=Mn(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!Cn.has(n)||2!==t.length)return;const[i,s]=t,o=Ln(i),r=Ln(s);if(o!==r)if(_e(o)&&_e(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else tn[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||jn(t[e]))&&n.push(e);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Xn.has(e)&&Ot(e).values.length&&(i=t[s]),s++}if(i&&n)for(const s of e)t[s]=Yn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=tn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const zn=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Hn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=at.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=at.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new k);const n=this.events[t].add(e);return"change"===t?()=>{n(),et.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=at.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return j(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function qn(t,e){return new Hn(t,e)}const{schedule:Gn,cancel:Zn}=tt(queueMicrotask,!1),_n={x:!1,y:!1};function Jn(){return _n.x||_n.y}function Qn(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let i=document;e&&(i=e.current);const s=n?.[t]??i.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function ti(t){return!("touch"===t.pointerType||Jn())}const ei=(t,e)=>!!e&&(t===e||ei(t,e.parentElement)),ni=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,ii=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const si=new WeakSet;function oi(t){return e=>{"Enter"===e.key&&t(e)}}function ri(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ai(t){return ni(t)&&!Jn()}function li(t,e,n={}){const[i,s,o]=Qn(t,n),r=t=>{const i=t.currentTarget;if(!ai(t))return;si.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),si.has(i)&&si.delete(i),ai(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||ei(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),Sn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=oi(()=>{if(si.has(n))return;ri(n,"down");const t=oi(()=>{ri(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>ri(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),e=t,ii.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function hi(t){return b(t)&&"ownerSVGElement"in t}const ui=t=>Boolean(t&&t.getVelocity),ci=[...Rn,Rt,Wt],di=t=>Array.isArray(t);function pi(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,qn(n))}function mi(t){return di(t)?t[t.length-1]||0:t}function fi(t,e){const n=t.getValue("willChange");if(i=n,Boolean(ui(i)&&i.add))return n.add(e);if(!n&&P.WillChange){const n=new P.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}const yi=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),gi="data-"+yi("framerAppearId");function vi(t){return t.props[gi]}const xi=t=>null!==t;const Ti={type:"spring",stiffness:500,damping:25,restSpeed:10},wi={type:"keyframes",duration:.8},Pi={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Si=(t,{keyframes:e})=>e.length>2?wi:Ze.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Ti:Pi;const bi=(t,e,n,i={},s,o)=>r=>{const a=Dn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:h=0}=i;h-=R(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length})(a)||Object.assign(u,Si(t,u)),u.duration&&(u.duration=R(u.duration)),u.repeatDelay&&(u.repeatDelay=R(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let c=!1;if((!1===u.type||0===u.duration&&!u.repeatDelay)&&(u.duration=0,0===u.delay&&(c=!0)),(P.instantAnimations||P.skipAnimations)&&(c=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,c&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(xi),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}(u.keyframes,a);if(void 0!==t)return void et.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new Ie(u):new En(u)};function Ai({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Ei(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e,t.latestValues[e]??null),s=a[e];if(void 0===s||h&&Ai(h,e))continue;const r={delay:n,...Dn(o||{},e)},u=i.get();if(void 0!==u&&!i.isAnimating&&!Array.isArray(s)&&s===u&&!r.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=vi(t);if(n){const t=window.MotionHandoffAnimation(n,e,et);null!==t&&(r.startTime=t,c=!0)}}fi(t,e),i.start(bi(e,i,s,t.shouldReduceMotion&&Cn.has(e)?{type:!1}:r,t,c));const d=i.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{et.update(()=>{r&&function(t,e){const n=v(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o)pi(t,e,mi(o[e]))}(t,r)})}),l}function Vi(t,e,n={}){const i=v(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Ei(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=0,o=1,r){const a=[],l=t.variantChildren.size,h=(l-1)*s,u="function"==typeof i,c=u?t=>i(t,l):1===o?(t=0)=>t*s:(t=0)=>h-t*s;return Array.from(t.variantChildren).sort(Mi).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(Vi(t,e,{...r,delay:n+(u?0:i)+c(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,i,o,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function Mi(t,e){return t.sortNodePosition(e)}function Di(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function Ci(t){return"string"==typeof t||Array.isArray(t)}const ki=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ri=["initial",...ki],Li=Ri.length;function ji(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&ji(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<Li;n++){const i=Ri[n],s=t.props[i];(Ci(s)||!1===s)&&(e[i]=s)}return e}const Bi=[...ki].reverse(),Fi=ki.length;function Oi(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>Vi(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=Vi(t,e,n);else{const s="function"==typeof e?v(t,e,n.custom):e;i=Promise.all(Ei(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function Ii(t){let e=Oi(t),n=Wi(),i=!0;const s=e=>(n,i)=>{const s=v(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=ji(t.parent)||{},l=[],h=new Set;let u={},c=1/0;for(let e=0;e<Fi;e++){const d=Bi[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],y=Ci(m),g=d===o?p.isActive:null;!1===g&&(c=e);let v=m===a[d]&&m!==r[d]&&y;if(v&&i&&t.manuallyAnimateOnMount&&(v=!1),p.protectedKeys={...u},!p.isActive&&null===g||!m&&!p.prevProp||f(m)||"boolean"==typeof m)continue;const x=Ui(p.prevProp,m);let T=x||d===o&&p.isActive&&!v&&y||e>c&&y,w=!1;const P=Array.isArray(m)?m:[m];let S=P.reduce(s(d),{});!1===g&&(S={});const{prevResolvedValues:b={}}=p,A={...b,...S},E=e=>{T=!0,h.has(e)&&(w=!0,h.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in A){const e=S[t],n=b[t];if(u.hasOwnProperty(t))continue;let i=!1;i=di(e)&&di(n)?!Di(e,n):e!==n,i?null!=e?E(t):h.add(t):void 0!==e&&h.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(u={...u,...S}),i&&t.blockInitialAnimation&&(T=!1);T&&(!(v&&x)||w)&&l.push(...P.map(t=>({animation:t,options:{type:d}})))}if(h.size){const e={};if("boolean"!=typeof r.initial){const n=v(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}h.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Wi(),i=!0}}}function Ui(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Di(e,t)}function Ni(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Wi(){return{animate:Ni(!0),whileInView:Ni(),whileHover:Ni(),whileTap:Ni(),whileDrag:Ni(),whileFocus:Ni(),exit:Ni()}}class $i{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Yi=0;const Xi={animation:{Feature:class extends $i{constructor(t){super(t),t.animationState||(t.animationState=Ii(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();f(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends $i{constructor(){super(...arguments),this.id=Yi++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Ki(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function zi(t){return{point:{x:t.pageX,y:t.pageY}}}function Hi(t,e,n,i){return Ki(t,e,(t=>e=>ni(e)&&t(e,zi(e)))(n),i)}function qi({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Gi(t){return t.max-t.min}function Zi(t,e,n,i=.5){t.origin=i,t.originPoint=Xt(e.min,e.max,t.origin),t.scale=Gi(n)/Gi(e),t.translate=Xt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function _i(t,e,n,i){Zi(t.x,e.x,n.x,i?i.originX:void 0),Zi(t.y,e.y,n.y,i?i.originY:void 0)}function Ji(t,e,n){t.min=n.min+e.min,t.max=t.min+Gi(e)}function Qi(t,e,n){t.min=e.min-n.min,t.max=t.min+Gi(e)}function ts(t,e,n){Qi(t.x,e.x,n.x),Qi(t.y,e.y,n.y)}const es=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ns(t){return[t("x"),t("y")]}function is(t){return void 0===t||1===t}function ss({scale:t,scaleX:e,scaleY:n}){return!is(t)||!is(e)||!is(n)}function os(t){return ss(t)||rs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function rs(t){return as(t.x)||as(t.y)}function as(t){return t&&"0%"!==t}function ls(t,e,n){return n+e*(t-n)}function hs(t,e,n,i,s){return void 0!==s&&(t=ls(t,s,i)),ls(t,n,i)+e}function us(t,e=0,n=1,i,s){t.min=hs(t.min,e,n,i,s),t.max=hs(t.max,e,n,i,s)}function cs(t,{x:e,y:n}){us(t.x,e.translate,e.scale,e.originPoint),us(t.y,n.translate,n.scale,n.originPoint)}const ds=.999999999999,ps=1.0000000000001;function ms(t,e){t.min=t.min+e,t.max=t.max+e}function fs(t,e,n,i,s=.5){us(t,e,n,Xt(t.min,t.max,s),i)}function ys(t,e){fs(t.x,e.x,e.scaleX,e.scale,e.originX),fs(t.y,e.y,e.scaleY,e.scale,e.originY)}function gs(t,e){return qi(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const vs=({current:t})=>t?t.ownerDocument.defaultView:null;function xs(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const Ts=(t,e)=>Math.abs(t-e);class ws{constructor(t,e,{transformPagePoint:n,contextWindow:i=window,dragSnapToOrigin:s=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=bs(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=Ts(t.x,e.x),i=Ts(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=it;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Ps(e,this.transformPagePoint),et.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=bs("pointercancel"===t.type?this.lastMoveEventInfo:Ps(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!ni(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=i||window;const r=Ps(zi(t),this.transformPagePoint),{point:a}=r,{timestamp:l}=it;this.history=[{...a,timestamp:l}];const{onSessionStart:h}=e;h&&h(t,bs(r,this.history)),this.removeListeners=D(Hi(this.contextWindow,"pointermove",this.handlePointerMove),Hi(this.contextWindow,"pointerup",this.handlePointerUp),Hi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),nt(this.updatePoint)}}function Ps(t,e){return e?{point:e(t.point)}:t}function Ss(t,e){return{x:t.x-e.x,y:t.y-e.y}}function bs({point:t},e){return{point:t,delta:Ss(t,Es(e)),offset:Ss(t,As(e)),velocity:Vs(e,.1)}}function As(t){return t[0]}function Es(t){return t[t.length-1]}function Vs(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Es(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>R(e)));)n--;if(!i)return{x:0,y:0};const o=L(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ms(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ds(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Cs=.35;function ks(t,e,n){return{min:Rs(t,e),max:Rs(t,n)}}function Rs(t,e){return"number"==typeof t?t:t[e]||0}const Ls=new WeakMap;class js{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:n}={}){const{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;const{dragSnapToOrigin:s}=this.getProps();this.panSession=new ws(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(zi(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?_n[o]?null:(_n[o]=!0,()=>{_n[o]=!1}):_n.x||_n.y?null:(_n.x=_n.y=!0,()=>{_n.x=_n.y=!1}),!this.openDragLock))return;var o;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ns(t=>{let e=this.getAxisMotionValue(t).get()||0;if(Et.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Gi(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&et.postRender(()=>s(t,e)),fi(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>ns(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:n,contextWindow:vs(this.visualElement)})}stop(t,e){const n=t||this.latestPointerEvent,i=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!i||!n)return;const{velocity:o}=i;this.startAnimation(o);const{onDragEnd:r}=this.getProps();r&&et.postRender(()=>r(n,i))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Bs(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Xt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Xt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&xs(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Ms(t.x,n,s),y:Ms(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=Cs){return!1===t?t=0:!0===t&&(t=Cs),{x:ks(t,"left","right"),y:ks(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&ns(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!xs(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=gs(t,n),{scroll:s}=e;return s&&(ms(i.x,s.offset.x),ms(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ds(t.x,e.x),y:Ds(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=qi(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=ns(r=>{if(!Bs(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const h=i?200:1e6,u=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:h,bounceDamping:u,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return fi(this.visualElement,t),n.start(bi(t,n,0,e,this.visualElement,!1))}stopAnimation(){ns(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ns(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){ns(e=>{const{drag:n}=this.getProps();if(!Bs(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Xt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!xs(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};ns(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Gi(t),s=Gi(e);return s>i?n=C(e.min,e.max-i,t.min):i>s&&(n=C(t.min,t.max-s,e.min)),w(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ns(e=>{if(!Bs(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Xt(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;Ls.set(this.visualElement,this);const t=Hi(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();xs(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),et.read(e);const s=Ki(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ns(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Cs,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Bs(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Fs=t=>(e,n)=>{t&&et.postRender(()=>t(e,n))};const Os=(t,e)=>t.depth-e.depth;class Is{constructor(){this.children=[],this.isDirty=!1}add(t){x(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Os),this.isDirty=!1,this.children.forEach(t)}}function Us(t){return ui(t)?t.get():t}const Ns=["TopLeft","TopRight","BottomLeft","BottomRight"],Ws=Ns.length,$s=t=>"string"==typeof t?parseFloat(t):t,Ys=t=>"number"==typeof t||Vt.test(t);function Xs(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Ks=Hs(0,.5,X),zs=Hs(.5,.95,V);function Hs(t,e,n){return i=>i<t?0:i>e?1:n(C(t,e,i))}function qs(t,e){t.min=e.min,t.max=e.max}function Gs(t,e){qs(t.x,e.x),qs(t.y,e.y)}function Zs(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function _s(t,e,n,i,s){return t=ls(t-=e,1/n,i),void 0!==s&&(t=ls(t,1/s,i)),t}function Js(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Et.test(e)&&(e=parseFloat(e),e=Xt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Xt(o.min,o.max,i);t===o&&(a-=e),t.min=_s(t.min,e,n,a,s),t.max=_s(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Qs=["x","scaleX","originX"],to=["y","scaleY","originY"];function eo(t,e,n,i){Js(t.x,e,Qs,n?n.x:void 0,i?i.x:void 0),Js(t.y,e,to,n?n.y:void 0,i?i.y:void 0)}function no(t){return 0===t.translate&&1===t.scale}function io(t){return no(t.x)&&no(t.y)}function so(t,e){return t.min===e.min&&t.max===e.max}function oo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ro(t,e){return oo(t.x,e.x)&&oo(t.y,e.y)}function ao(t){return Gi(t.x)/Gi(t.y)}function lo(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ho{constructor(){this.members=[]}add(t){x(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const uo={};const co={hasAnimatedSinceResize:!0,hasEverUpdated:!1},po=["","X","Y","Z"];let mo=0;function fo(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function yo(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=vi(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",et,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&yo(i)}function go({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=mo++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(To),this.nodes.forEach(Vo),this.nodes.forEach(Mo),this.nodes.forEach(wo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Is)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new k),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=hi(e)&&!(hi(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n,i=0;const s=()=>this.root.updateBlockedByResize=!1;et.read(()=>{i=window.innerWidth}),t(e,()=>{const t=window.innerWidth;t!==i&&(i=t,this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=at.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(nt(i),t(o-e))};return et.setup(i,!0),()=>nt(i)}(s,250),co.hasAnimatedSinceResize&&(co.hasAnimatedSinceResize=!1,this.nodes.forEach(Eo)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||jo,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!ro(this.targetLayout,i),h=!e&&n;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...Dn(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||Eo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),nt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Do),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&yo(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(So);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(bo);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Ao),this.nodes.forEach(vo),this.nodes.forEach(xo)):this.nodes.forEach(bo),this.clearAllSnapshots();const t=at.now();it.delta=w(0,1e3/60,t-it.timestamp),it.timestamp=t,it.isProcessing=!0,st.update.process(it),st.preRender.process(it),st.render.process(it),it.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Gn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Po),this.sharedNodes.forEach(Co)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,et.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){et.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Gi(this.snapshot.measuredBox.x)||Gi(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!io(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||os(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Oo((i=n).x),Oo(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Uo))){const{scroll:t}=this.root;t&&(ms(e.x,t.offset.x),ms(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Gs(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Gs(e,t),ms(e.x,s.offset.x),ms(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Gs(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ys(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),os(i.latestValues)&&ys(n,i.latestValues)}return os(this.latestValues)&&ys(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Gs(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!os(n.latestValues))continue;ss(n.latestValues)&&n.updateSnapshot();const i=es();Gs(i,n.measurePageBox()),eo(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return os(this.latestValues)&&eo(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==it.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=it.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ts(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Gs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Ji(o.x,r.x,a.x),Ji(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Gs(this.target,this.layout.layoutBox),cs(this.target,this.targetDelta)):Gs(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ts(this.relativeTargetOrigin,this.target,t.target),Gs(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!ss(this.parent.latestValues)&&!rs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===it.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;Gs(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ys(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,cs(t,r)),i&&os(o.latestValues)&&ys(t,o.latestValues))}e.x<ps&&e.x>ds&&(e.x=1),e.y<ps&&e.y>ds&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Zs(this.prevProjectionDelta.x,this.projectionDelta.x),Zs(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),_i(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&lo(this.projectionDelta.x,this.prevProjectionDelta.x)&&lo(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),h=!l||l.members.length<=1,u=Boolean(a&&!h&&!0===this.options.crossfade&&!this.path.some(Lo));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;ko(o.x,t.x,n),ko(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ts(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,Ro(p.x,m.x,f.x,y),Ro(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,so(l.x,d.x)&&so(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),Gs(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Xt(0,n.opacity??1,Ks(i)),t.opacityExit=Xt(e.opacity??1,0,zs(i))):o&&(t.opacity=Xt(e.opacity??1,n.opacity??1,i));for(let s=0;s<Ws;s++){const o=`border${Ns[s]}Radius`;let r=Xs(e,o),a=Xs(n,o);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||Ys(r)===Ys(a)?(t[o]=Math.max(Xt($s(r),$s(a),i),0),(Et.test(a)||Et.test(r))&&(t[o]+="%")):t[o]=a)}(e.rotate||n.rotate)&&(t.rotate=Xt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,u,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(nt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=et.update(()=>{co.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=qn(0)),this.currentAnimation=function(t,e,n){const i=ui(t)?t:qn(t);return i.start(bi("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Io(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Gi(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Gi(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Gs(e,n),ys(e,s),_i(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new ho);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&fo("z",t,i,this.animationValues);for(let e=0;e<po.length;e++)fo(`rotate${po[e]}`,t,i,this.animationValues),fo(`skew${po[e]}`,t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return void(t.visibility="hidden");const n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=Us(e?.pointerEvents)||"",void(t.transform=n?n(this.latestValues,""):"none");const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target)return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Us(e?.pointerEvents)||""),void(this.hasProjected&&!os(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1));t.visibility="";const s=i.animationValues||i.latestValues;this.applyTransformsToTarget();let o=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);n&&(o=n(s,o)),t.transform=o;const{x:r,y:a}=this.projectionDelta;t.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,i.animationValues?t.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const e in uo){if(void 0===s[e])continue;const{correct:n,applyTo:r,isCSSVariable:a}=uo[e],l="none"===o?s[e]:n(s[e],i);if(r){const e=r.length;for(let n=0;n<e;n++)t[r[n]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=i===this?Us(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(So),this.root.sharedNodes.clear()}}}function vo(t){t.updateLayout()}function xo(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?ns(t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=Gi(i);i.min=n[t].min,i.max=i.min+s}):Io(s,e.layoutBox,n)&&ns(i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=Gi(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};_i(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?_i(a,t.applyTransform(i,!0),e.measuredBox):_i(a,n,e.layoutBox);const l=!io(r);let h=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};ts(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};ts(a,n,o.layoutBox),ro(r,a)||(h=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function To(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function wo(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Po(t){t.clearSnapshot()}function So(t){t.clearMeasurements()}function bo(t){t.isLayoutDirty=!1}function Ao(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Eo(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Vo(t){t.resolveTargetDelta()}function Mo(t){t.calcProjection()}function Do(t){t.resetSkewAndRotation()}function Co(t){t.removeLeadSnapshot()}function ko(t,e,n){t.translate=Xt(e.translate,0,n),t.scale=Xt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ro(t,e,n,i){t.min=Xt(e.min,n.min,i),t.max=Xt(e.max,n.max,i)}function Lo(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const jo={duration:.45,ease:[.4,0,.1,1]},Bo=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Fo=Bo("applewebkit/")&&!Bo("chrome/")?Math.round:V;function Oo(t){t.min=Fo(t.min),t.max=Fo(t.max)}function Io(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=ao(e),s=ao(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Uo(t){return t!==t.root&&t.scroll?.wasRoot}const No=go({attachResizeListener:(t,e)=>Ki(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Wo={current:void 0},$o=go({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Wo.current){const t=new No({});t.mount(window),t.setOptions({layoutScroll:!0}),Wo.current=t}return Wo.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function Yo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Xo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Vt.test(t))return t;t=parseFloat(t)}return`${Yo(t,e.target.x)}% ${Yo(t,e.target.y)}%`}},Ko={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Wt.parse(t);if(s.length>5)return i;const o=Wt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const h=Xt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=h),"number"==typeof s[3+r]&&(s[3+r]/=h),o(s)}},zo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Ho={};for(const t in zo)Ho[t]={isEnabled:e=>zo[t].some(t=>!!e[t])};const qo="undefined"!=typeof window,Go={current:null},Zo={current:!1};const _o=new WeakMap;function Jo(t){return f(t.animate)||Ri.some(e=>Ci(t[e]))}function Qo(t){return Boolean(Jo(t)||t.variants)}const tr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class er{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ln,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=at.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,et.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Jo(e),this.isVariantNode=Qo(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in u){const e=u[t];void 0!==a[t]&&ui(e)&&e.set(a[t],!1)}}mount(t){this.current=t,_o.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Zo.current||function(){if(Zo.current=!0,qo)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Go.current=t.matches;t.addEventListener("change",e),e()}else Go.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Go.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),nt(this.notifyUpdate),nt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ze.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&et.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Ho){const e=Ho[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<tr.length;e++){const n=tr[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(ui(s))t.addValue(i,s);else if(ui(o))t.addValue(i,qn(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,qn(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=qn(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(S(n)||A(n))?n=parseFloat(n):(i=n,!ci.find(kn(i))&&Wt.test(e)&&(n=Yn(t,e))),this.setBaseTarget(t,ui(n)?n.get():n)),ui(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=g(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||ui(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new k),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nr extends er{constructor(){super(...arguments),this.KeyframeResolver=Kn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ui(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}const ir={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sr=Ge.length;function or(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(Ze.has(t))r=!0;else if(ht(t))s[t]=n;else{const e=zn(n,Nn[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<sr;o++){const r=Ge[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=zn(a,Nn[r]);l||(s=!1,i+=`${ir[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function rr(t,{style:e,vars:n},i,s){const o=t.style;let r;for(r in e)o[r]=e[r];for(r in s?.applyProjectionStyles(o,i),n)o.setProperty(r,n[r])}function ar(t,{layout:e,layoutId:n}){return Ze.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!uo[t]||"opacity"===t)}function lr(t,e,n){const{style:i}=t,s={};for(const o in i)(ui(i[o])||e.style&&ui(e.style[o])||ar(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}class hr extends nr{constructor(){super(...arguments),this.type="html",this.renderInstance=rr}readValueFromInstance(t,e){if(Ze.has(e))return this.projection?.isProjecting?ze(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return He(n,e)})(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(ht(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return gs(t,e)}build(t,e,n){or(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return lr(t,e,n)}}const ur=n(null);const cr=n({}),dr=n({});let pr=!1;class mr extends a{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)uo[e]=t[e],ht(e)&&(uo[e].isCSSVariable=!0)}(yr),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),pr&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),co.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,pr=!0,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||et.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Gn.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function fr(e){const[n,a]=function(t=!0){const e=i(ur);if(null===e)return[!0,null];const{isPresent:n,onExitComplete:a,register:l}=e,h=s();o(()=>{if(t)return l(h)},[t]);const u=r(()=>t&&a&&a(h),[h,a,t]);return!n&&a?[!1,u]:[!0]}(),l=i(cr);return t(mr,{...e,layoutGroup:l,switchLayoutGroup:i(dr),isPresent:n,safeToRemove:a})}const yr={borderRadius:{...Xo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Xo,borderTopRightRadius:Xo,borderBottomLeftRadius:Xo,borderBottomRightRadius:Xo,boxShadow:Ko},gr={pan:{Feature:class extends $i{constructor(){super(...arguments),this.removePointerDownListener=V}onPointerDown(t){this.session=new ws(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:vs(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Fs(t),onStart:Fs(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&et.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Hi(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends $i{constructor(t){super(t),this.removeGroupControls=V,this.removeListeners=V,this.controls=new js(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||V}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:$o,MeasureLayout:fr}};function vr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&et.postRender(()=>s(e,zi(e)))}function xr(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&et.postRender(()=>s(e,zi(e)))}const Tr=new WeakMap,wr=new WeakMap,Pr=t=>{const e=Tr.get(t.target);e&&e(t)},Sr=t=>{t.forEach(Pr)};function br(t,e,n){const i=function({root:t,...e}){const n=t||document;wr.has(n)||wr.set(n,{});const i=wr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Sr,{root:t,...e})),i[s]}(e);return Tr.set(t,n),i.observe(t),()=>{Tr.delete(t),i.unobserve(t)}}const Ar={some:0,all:1};const Er={inView:{Feature:class extends $i{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Ar[i]};return br(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends $i{mount(){const{current:t}=this.node;t&&(this.unmount=li(t,(t,e)=>(xr(this.node,e,"Start"),(t,{success:e})=>xr(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends $i{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=D(Ki(this.node.current,"focus",()=>this.onFocus()),Ki(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends $i{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Qn(t,n),r=t=>{if(!ti(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{ti(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,(t,e)=>(vr(this.node,e,"Start"),t=>vr(this.node,t,"End"))))}unmount(){}}}},Vr={layout:{ProjectionNode:$o,MeasureLayout:fr}},Mr=n({strict:!1}),Dr=n({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),Cr=n({});function kr(t){const{initial:e,animate:n}=function(t,e){if(Jo(t)){const{initial:e,animate:n}=t;return{initial:!1===e||Ci(e)?e:void 0,animate:Ci(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,i(Cr));return l(()=>({initial:e,animate:n}),[Rr(e),Rr(n)])}function Rr(t){return Array.isArray(t)?t.join(" "):t}const Lr=Symbol.for("motionComponentSymbol");function jr(t,e,n){return r(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&("function"==typeof n?n(i):xs(n)&&(n.current=i))},[e])}const Br=qo?h:o;function Fr(t,e,n,s,r){const{visualElement:a}=i(Cr),l=i(Mr),h=i(ur),d=i(Dr).reducedMotion,p=u(null);s=s||l.renderer,!p.current&&s&&(p.current=s(t,{visualState:e,parent:a,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:d}));const m=p.current,f=i(dr);!m||m.projection||!r||"html"!==m.type&&"svg"!==m.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Or(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&xs(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,n,r,f);const y=u(!1);c(()=>{m&&y.current&&m.update(n,h)});const g=n[gi],v=u(Boolean(g)&&!window.MotionHandoffIsComplete?.(g)&&window.MotionHasOptimisedAnimation?.(g));return Br(()=>{m&&(y.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),Gn.render(m.render),v.current&&m.animationState&&m.animationState.animateChanges())}),o(()=>{m&&(!v.current&&m.animationState&&m.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(g)}),v.current=!1))}),m}function Or(t){if(t)return!1!==t.options.allowProjection?t.projection:Or(t.parent)}function Ir({preloadedFeatures:n,createVisualElement:s,useRender:o,useVisualState:r,Component:a}){function l(n,l){let h;const u={...i(Dr),...n,layoutId:Ur(n)},{isStatic:c}=u,d=kr(n),p=r(n,c);if(!c&&qo){i(Mr).strict;const t=function(t){const{drag:e,layout:n}=Ho;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(u);h=t.MeasureLayout,d.visualElement=Fr(a,p,u,s,t.ProjectionNode)}return e(Cr.Provider,{value:d,children:[h&&d.visualElement?t(h,{visualElement:d.visualElement,...u}):null,o(a,n,jr(p,d.visualElement,l),p,c,d.visualElement)]})}n&&function(t){for(const e in t)Ho[e]={...Ho[e],...t[e]}}(n),l.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;const h=d(l);return h[Lr]=a,h}function Ur({layoutId:t}){const e=i(cr).id;return e&&void 0!==t?e+"-"+t:t}const Nr=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Wr(t,e,n){for(const i in e)ui(e[i])||ar(i,n)||(t[i]=e[i])}function $r(t,e){const n={};return Wr(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},e){return l(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{}};return or(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),n}function Yr(t,e){const n={},i=$r(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Xr={offset:"stroke-dashoffset",array:"stroke-dasharray"},Kr={offset:"strokeDashoffset",array:"strokeDasharray"};function zr(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,h,u){if(or(t,a,h),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Xr:Kr;t[o.offset]=Vt.transform(-i);const r=Vt.transform(e),a=Vt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const Hr=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),qr=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Gr(t,e,n,i){const s=l(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return zr(n,e,qr(i),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){const e={};Wr(e,t.style,t),s.style={...e,...s.style}}return s}const Zr=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function _r(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Zr.has(t)}let Jr=t=>!_r(t);try{"function"==typeof(Qr=require("@emotion/is-prop-valid").default)&&(Jr=t=>t.startsWith("on")?!_r(t):Qr(t))}catch{}var Qr;const ta=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ea(t){return"string"==typeof t&&!t.includes("-")&&!!(ta.indexOf(t)>-1||/[A-Z]/u.test(t))}function na(t=!1){return(e,n,i,{latestValues:s},o)=>{const r=(ea(e)?Gr:Yr)(n,s,o,e),a=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(Jr(s)||!0===n&&_r(s)||!e&&!_r(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(n,"string"==typeof e,t),h=e!==p?{...a,...r,ref:i}:{},{children:u}=n,c=l(()=>ui(u)?u.get():u,[u]);return m(e,{...h,children:c})}}const ia=t=>(e,n)=>{const s=i(Cr),o=i(ur),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:sa(n,i,s,t),renderState:e()}}(t,e,s,o);return n?r():function(t){const e=u(null);return null===e.current&&(e.current=t()),e.current}(r)};function sa(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=Us(o[t]);let{initial:r,animate:a}=t;const l=Jo(t),h=Qo(t);e&&h&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let u=!!n&&!1===n.initial;u=u||!1===r;const c=u?a:r;if(c&&"boolean"!=typeof c&&!f(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=g(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[u?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const oa={useVisualState:ia({scrapeMotionValuesFromProps:lr,createRenderState:Nr})};function ra(t,e,n){const i=lr(t,e,n);for(const n in t)if(ui(t[n])||ui(e[n])){i[-1!==Ge.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const aa={useVisualState:ia({scrapeMotionValuesFromProps:ra,createRenderState:Hr})};function la(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Ir({...ea(n)?aa:oa,preloadedFeatures:t,useRender:na(i),createVisualElement:e,Component:n})}}const ha=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ua extends nr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=es}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Ze.has(e)){const t=$n(e);return t&&t.default||0}return e=ha.has(e)?e:yi(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ra(t,e,n)}build(t,e,n){zr(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){rr(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(ha.has(n)?n:yi(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=qr(t.tagName),super.mount(t)}}const ca=(t,e)=>ea(t)?new ua(e):new hr(e,{allowProjection:t!==p}),da=la({...Xi,...Er,...gr,...Vr},ca)("div");export{da as MotionDiv};
